#!/usr/bin/env python3
"""
Скрипт для удаления однострочных комментариев из Python файла
Удаляет:
- Однострочные комментарии (#)
- Сохраняет строки с кодом и docstrings
"""

import re
import sys
import os

def remove_comments_from_file(input_file, output_file=None):
    """
    Удаляет однострочные комментарии из Python файла

    Args:
        input_file (str): Путь к исходному файлу
        output_file (str): Путь к выходному файлу (если None, перезаписывает исходный)
    """
    
    if not os.path.exists(input_file):
        print(f"Ошибка: Файл {input_file} не найден!")
        return False
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Разбиваем на строки для обработки однострочных комментариев
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Проверяем, не находится ли # внутри строки
            in_string = False
            quote_char = None
            i = 0
            
            while i < len(line):
                char = line[i]
                
                # Обрабатываем экранированные символы
                if char == '\\' and i + 1 < len(line):
                    i += 2
                    continue
                
                # Обрабатываем строки
                if char in ['"', "'"] and not in_string:
                    in_string = True
                    quote_char = char
                elif char == quote_char and in_string:
                    in_string = False
                    quote_char = None
                
                # Если нашли # вне строки, обрезаем строку
                elif char == '#' and not in_string:
                    line = line[:i].rstrip()
                    break
                
                i += 1
            
            # Добавляем строку, если она не пустая или содержит только пробелы
            if line.strip() or not line:
                cleaned_lines.append(line)
        
        # Объединяем строки обратно
        cleaned_content = '\n'.join(cleaned_lines)
        
        # Удаляем лишние пустые строки (более 2 подряд)
        cleaned_content = re.sub(r'\n{3,}', '\n\n', cleaned_content)
        
        # Определяем выходной файл
        if output_file is None:
            output_file = input_file
        
        # Записываем результат
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print(f"✅ Однострочные комментарии удалены из {input_file}")
        if output_file != input_file:
            print(f"   Результат сохранен в {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при обработке файла: {e}")
        return False

def main():
    """Основная функция скрипта"""
    
    # Путь к файлу плагина
    plugin_file = "voice_transcription.plugin"
    
    # Можно указать выходной файл или оставить None для перезаписи
    output_file = None  # Или "voice_transcription_no_comments.plugin"
    
    print("🧹 Скрипт удаления однострочных комментариев из Python кода")
    print("=" * 50)
    
    if not os.path.exists(plugin_file):
        print(f"❌ Файл {plugin_file} не найден в текущей директории!")
        print("   Убедитесь, что скрипт находится в той же папке, что и плагин.")
        return
    
    # Создаем резервную копию
    backup_file = plugin_file + ".backup"
    try:
        with open(plugin_file, 'r', encoding='utf-8') as src:
            with open(backup_file, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
        print(f"📋 Создана резервная копия: {backup_file}")
    except Exception as e:
        print(f"⚠️  Не удалось создать резервную копию: {e}")
    
    # Удаляем комментарии
    success = remove_comments_from_file(plugin_file, output_file)
    
    if success:
        print("\n✨ Готово! Все однострочные комментарии удалены.")
        print("💡 Резервная копия сохранена с расширением .backup")
    else:
        print("\n❌ Произошла ошибка при удалении однострочных комментариев.")

if __name__ == "__main__":
    main()
